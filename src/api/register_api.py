# The MIT License (MIT)
# Copyright © 2023 Crazydevlegend
# Copyright © 2023 Rapiiidooo
# Copyright @ 2024 Skynet
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the “Software”), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all copies or substantial portions of
# the Software.
#
# THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
# THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
# DEALINGS IN THE SOFTWARE.
# Step 1: Import necessary libraries and modules

# Standard Libraries
import os
import json
import asyncio
import multiprocessing
from datetime import datetime, timezone

# Third-party Libraries
import urllib3
from urllib3.exceptions import InsecureRequestWarning
from dotenv import load_dotenv
import bittensor as bt
import uvicorn
from fastapi import (
    FastAPI,
    status,
    Request,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from typing import Optional, List
import sqlite3
from starlette.middleware.base import BaseHTTPMiddleware

# Custom Libraries
from compute.axon import ComputeSubnetSubtensor
from compute.utils.db import ComputeDb
from compute.wandb.wandb import ComputeWandb
from compute.protocol import Allocate
from neurons.Validator.database.allocate import (
    update_allocation_db,
)

from api.config import _init_config
from api.models.register import (
    DeviceRequirement,
    DockerRequirement,
    ResourceQuery,
    IPWhitelistMiddleware,
)
from api.models.response import (
    SuccessResponse,
    ErrorResponse,
)

# Services
from api.services.allocation_service import (
    allocate_spec,
    allocate_hotkey,
    deallocate,
    check_miner_status,
    set_docker_action,
    _update_allocation_wandb,
    _notify_allocation_status,
)
from api.services.sql_service import (
    list_allocations,
    list_resources,
    list_associated_hotkeys,
)
from api.services.wandb_service import (
    count_all_model,
    count_all_gpus,
    list_resources_wandb,
    list_all_runs,
    list_specs,
    list_run_name,
    list_available_miner,
    list_allocated_hotkeys,
)
from api.services.testing_service import test_notify

# Constants
from api.constants import (
    DEFAULT_API_PORT,
    DATA_SYNC_PERIOD,
    ALLOCATE_CHECK_PERIOD,
    ALLOCATE_CHECK_COUNT,
    DEFAULT_SSL_MODE,
    ENABLE_WHITELIST_IPS,
)

# Disable InsecureRequestWarning from urllib3
urllib3.disable_warnings(InsecureRequestWarning)

load_dotenv()


class RegisterAPI:
    def __init__(
        self,
        config: Optional[bt.config] = None,
        wallet: Optional[bt.wallet] = None,
        subtensor: Optional[bt.subtensor] = None,
        dendrite: Optional[bt.dendrite] = None,
        metagraph: Optional[bt.metagraph] = None,  # type: ignore
        wandb: Optional[ComputeWandb] = None,
    ):
        # Compose User Config Data with bittensor config
        # Get the config from the user config
        if config is None:
            # Step 1: Parse the bittensor and compute subnet config
            self.config = _init_config()

            # Set up logging with the provided configuration and directory.
            bt.logging.set_debug(self.config.logging.debug)
            bt.logging.set_trace(self.config.logging.trace)
            bt.logging(config=self.config, logging_dir=self.config.full_path)
            bt.logging.info(
                f"Running validator register for subnet: {self.config.netuid} "
                f"on network: {self.config.subtensor.chain_endpoint} with config:"
            )

            # Log the configuration for reference.
            bt.logging.info(self.config)
            bt.logging.info("Setting up bittensor objects.")

            # The wallet holds the cryptographic key pairs for the validator.
            self.wallet = bt.wallet(config=self.config)
            bt.logging.info(f"Wallet: {self.wallet}")

            self.wandb = ComputeWandb(self.config, self.wallet, "validator.py")

            # The subtensor is our connection to the Bittensor blockchain.
            self.subtensor = ComputeSubnetSubtensor(config=self.config)
            bt.logging.info(f"Subtensor: {self.subtensor}")

            # Dendrite is the RPC client; it lets us send messages to other nodes (axons) in the network.
            self.dendrite = bt.dendrite(wallet=self.wallet)
            self.dendrite_check = bt.dendrite(wallet=self.wallet)
            bt.logging.info(f"Dendrite: {self.dendrite}")

            # The metagraph holds the state of the network, letting us know about other miners.
            self.metagraph = self.subtensor.metagraph(self.config.netuid)
            bt.logging.info(f"Metagraph: {self.metagraph}")

            # Set the IP address and port for the API server
            if self.config.axon.ip == "[::]":
                self.ip_addr = "0.0.0.0"
            else:
                self.ip_addr = self.config.axon.ip

            if self.config.axon.port is None:
                self.port = DEFAULT_API_PORT
            else:
                self.port = self.config.axon.port

        else:
            self.config = config.copy()
            # Wallet is the keypair that lets us sign messages to the blockchain.
            self.wallet = wallet
            # The subtensor is our connection to the Bittensor blockchain.
            self.subtensor = subtensor
            # Dendrite is the RPC client; it lets us send messages to other nodes (axons) in the network.
            self.dendrite = dendrite
            # The metagraph holds the state of the network, letting us know about other miners.
            self.metagraph = metagraph
            # Initialize the W&B logging
            self.wandb = wandb

            if self.config.axon.ip == "[::]":
                self.ip_addr = "0.0.0.0"
            else:
                self.ip_addr = self.config.axon.ip

            if self.config.axon.port is None:
                self.port = DEFAULT_API_PORT
            else:
                self.port = self.config.axon.port

        if self.config.logging.trace:
            self.app = FastAPI(debug=False, lifespan=self.lifespan)
        else:
            self.app = FastAPI(
                debug=False, docs_url="/docs", redoc_url=None, lifespan=self.lifespan
            )
        # load_dotenv()
        self._setup_routes()

        if ENABLE_WHITELIST_IPS:
            self.app.add_middleware(IPWhitelistMiddleware)

        self.process = None
        self.websocket_connection = None
        self.allocation_table = []
        self.checking_allocated = []
        self.notify_retry_table = []
        self.deallocation_notify_url = os.getenv("DEALLOCATION_NOTIFY_URL")
        self.status_notify_url = os.getenv("STATUS_NOTIFY_URL")
        self.db = ComputeDb()
        self.db.conn.close()
        db_path = (
            "database.db"  # TODO: Compute() need to set sqlite db location by argument
        )
        if os.path.exists(db_path):
            os.remove(db_path)
        self.db.conn = sqlite3.connect(
            os.getenv("SQLITE_DB_PATH"), check_same_thread=False
        )
        bt.logging.info(
            f"Sqlite db location has changed: {db_path}->{os.getenv('SQLITE_DB_PATH')}"
        )


    @asynccontextmanager
    async def lifespan(self, app: FastAPI):
        """
        This function is called when the application starts.
        It initializes the database connection and other necessary components.
        """
        # Setup the repeated task

        self.metagraph_task = asyncio.create_task(self._refresh_metagraph())
        self.allocate_check_task = asyncio.create_task(self._check_allocation())
        bt.logging.info(
            f"Register API server is started on https://{self.ip_addr}:{self.port}"
        )

        yield

        # This function is called when the application stops.
        bt.logging.info("Stopping the API server")
        self.db.close()
        pass


    def _setup_routes(self):
        # Define a custom validation error handler
        @self.app.exception_handler(RequestValidationError)
        async def validation_exception_handler(
            request: Request, exc: RequestValidationError
        ):
            # Customize the error response
            errors = exc.errors()
            custom_errors = [
                {"field": err["loc"][-1], "message": err["msg"]} for err in errors
            ]
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "success": False,
                    "message": "Validation Error, Please check the request body.",
                    "err_detail": custom_errors,
                },
            )

        # Entry point for the API
        @self.app.get("/", tags=["Root"])
        async def read_root():
            return {
                "message": "Welcome to Compute Subnet Allocation API, Please access the API via endpoint."
            }

        @self.app.websocket(path="/connect", name="websocket")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            self.websocket_connection = websocket
            bt.logging.info("API: Websocket connection established")
            while True:
                try:
                    # data = await websocket.receive_text()
                    msg = {
                        "type": "keepalive",
                        "payload": {
                            "time": datetime.now(timezone.utc).strftime(
                                "%Y-%m-%dT%H:%M:%S.%fZ"
                            ),
                        },
                    }
                    await websocket.send_text(json.dumps(msg))
                    await asyncio.sleep(30)
                except WebSocketDisconnect:
                    bt.logging.info("API: Websocket connection closed")
                    await self.websocket_connection.close()
                    self.websocket_connection = None
                    break

        @self.app.post(
            "/service/allocate_spec",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource was successfully allocated",
                },
                400: {
                    "model": ErrorResponse,
                    "description": "Invalid allocation request",
                },
                401: {
                    "model": ErrorResponse,
                    "description": "Missing authorization",
                },
                404: {
                    "model": ErrorResponse,
                    "description": "Fail to allocate resource",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def allocate_spec_handler(
            requirements: DeviceRequirement, docker_requirement: DockerRequirement
        ) -> JSONResponse:
            # Call the service method to allocate resources
            return await allocate_spec(
                self.db,
                self.wandb,
                self.metagraph,
                self.dendrite,
                requirements,
                docker_requirement,
            )

        @self.app.post(
            "/service/allocate_hotkey",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource was successfully allocated",
                },
                400: {
                    "model": ErrorResponse,
                    "description": "Invalid allocation request",
                },
                401: {
                    "model": ErrorResponse,
                    "description": "Missing authorization",
                },
                404: {
                    "model": ErrorResponse,
                    "description": "Fail to allocate resource",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def allocate_hotkey_handler(
            hotkey: str,
            ssh_key: Optional[str] = None,
            docker_requirement: Optional[DockerRequirement] = None,
        ) -> JSONResponse:
            return await allocate_hotkey(
                self.db,
                self.metagraph,
                self.dendrite,
                self.wandb,
                hotkey,
                ssh_key,
                docker_requirement,
            )

        @self.app.post(
            "/service/deallocate",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource deallocated successfully.",
                },
                400: {
                    "model": ErrorResponse,
                    "description": "Deallocation not successfully, please try again.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred during de-allocation",
                },
                404: {
                    "model": ErrorResponse,
                    "description": "No allocation details found for the provided hotkey.",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def deallocate_handler(
            hotkey: str, uuid_key: str, request: Request, notify_flag: bool = False
        ) -> JSONResponse:
            return await deallocate(
                db=self.db,
                wandb=self.wandb,
                deallocation_notify_url=self.deallocation_notify_url,
                status_notify_url=self.status_notify_url,
                notify_retry_table=self.notify_retry_table,
                metagraph=self.metagraph,
                dendrite=self.dendrite,
                hotkey=hotkey,
                uuid_key=uuid_key,
                request=request,
                notify_flag=notify_flag,
            )

        @self.app.post(
            path="/service/check_miner_status",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource deallocated successfully.",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while retrieving hotkey status.",
                },
            },
        )
        async def check_miner_status_handler(
            hotkey_list: List[str],
            query_version: bool = False,
        ) -> JSONResponse:
            return await check_miner_status(self.metagraph, self.dendrite, hotkey_list, query_version)

        @self.app.post(
            path="/service/restart_docker",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource restart successfully.",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while restarting docker.",
                },
            },
        )
        async def restart_docker_handler(
            hotkey: str,
            uuid_key: str,
        ) -> JSONResponse:
            return await set_docker_action(
                action="restart",
                db=self.db,
                ssh_key="",
                metagraph=self.metagraph,
                dendrite=self.dendrite,
                hotkey=hotkey,
                uuid_key=uuid_key,
            )

        @self.app.post(
            path="/service/pause_docker",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource pause successfully.",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while pausing docker.",
                },
            },
        )
        async def pause_docker_handler(
            hotkey: str,
            uuid_key: str,
        ) -> JSONResponse:
            return await set_docker_action(
                action="pause",
                db=self.db,
                ssh_key="",
                metagraph=self.metagraph,
                dendrite=self.dendrite,
                hotkey=hotkey,
                uuid_key=uuid_key,
            )

        @self.app.post(
            path="/service/unpause_docker",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource unpause successfully.",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while unpausing docker.",
                },
            },
        )
        async def unpause_docker_handler(
            hotkey: str,
            uuid_key: str,
        ) -> JSONResponse:
            return await set_docker_action(
                action="unpause",
                db=self.db,
                ssh_key="",
                metagraph=self.metagraph,
                dendrite=self.dendrite,
                hotkey=hotkey,
                uuid_key=uuid_key,
            )

        @self.app.post(
            "/service/exchange_docker_key",
            tags=["Allocation"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Resource ssh_key was changed successfully.",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while exchanging docker key.",
                },
            },
        )
        async def exchange_docker_key_handler(
            hotkey: str,
            uuid_key: str,
            ssh_key: str,
            key_type: str = "user",
        ) -> JSONResponse:
            return await set_docker_action(
                action="exchange_key",
                db=self.db,
                ssh_key=ssh_key,
                metagraph=self.metagraph,
                dendrite=self.dendrite,
                hotkey=hotkey,
                uuid_key=uuid_key,
                key_type=key_type,
            )

        @self.app.post(
            "/list/allocations_sql",
            tags=["SQLite"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List allocations successfully.",
                },
                401: {
                    "model": ErrorResponse,
                    "description": "Missing authorization token",
                },
                403: {
                    "model": ErrorResponse,
                    "description": "An error occurred while retrieving allocation details",
                },
                404: {
                    "model": ErrorResponse,
                    "description": "There is no allocation available",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_allocations_handler() -> JSONResponse:
            return await list_allocations(db=self.db, checking_allocated=self.checking_allocated)

        @self.app.post(
            "/list/resources_sql",
            tags=["SQLite"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "There is no resource available",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_resources_handler(
            query: ResourceQuery = None,
            stats: bool = False,
            page_size: Optional[int] = None,
            page_number: Optional[int] = None,
        ) -> JSONResponse:
            return await list_resources(
                self.db,
                self.config,
                self.wandb,
                self.metagraph,
                query,
                stats,
                page_size,
                page_number,
            )

        @self.app.post(
            "/list/count_all_gpus",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "There is no resource available",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def count_all_gpus_handler() -> JSONResponse:
            return await count_all_gpus(self.config, self.wandb, self.metagraph)

        @self.app.post(
            "/list/count_all_by_model",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "There is no resource available",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def count_all_model_handler(
            model: str,
            cpu_count: Optional[int] = None,
            ram_size: Optional[float] = None,
        ) -> JSONResponse:
            return await count_all_model(
                self.config, self.wandb, self.metagraph, model, cpu_count, ram_size
            )

        @self.app.post(
            "/list/resources_wandb",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "There is no resource available",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_resources_wandb_handler(
            query: ResourceQuery = None,
            stats: bool = False,
            page_size: Optional[int] = None,
            page_number: Optional[int] = None,
        ) -> JSONResponse:
            return await list_resources_wandb(
                self.db,
                self.config,
                self.wandb,
                self.metagraph,
                query,
                stats,
                page_size,
                page_number,
            )

        @self.app.post(
            "/list/all_runs",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List run resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while getting runs from wandb",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_all_runs_handler(
            hotkey: Optional[str] = None,
            page_size: Optional[int] = None,
            page_number: Optional[int] = None,
        ) -> JSONResponse:
            return await list_all_runs(
                self.config, self.wandb, hotkey, page_size, page_number
            )

        @self.app.post(
            "/list/specs",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List spec resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while getting specs from wandb",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_specs_handler(
            hotkey: Optional[str] = None,
            page_size: Optional[int] = None,
            page_number: Optional[int] = None,
        ) -> JSONResponse:
            return await list_specs(
                self.config, self.wandb, hotkey, page_size, page_number
            )

        @self.app.post(
            "/list/run_by_name",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List run resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while getting run from wandb",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_run_name_handler(
            run_name: str,
        ) -> JSONResponse:
            return await list_run_name(self.config, self.wandb, run_name)

        @self.app.post(
            "/list/available",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List available resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while fetch available miner from wandb",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_available_miner_handler(
            rent_status: bool = False,
            page_size: Optional[int] = None,
            page_number: Optional[int] = None,
        ) -> JSONResponse:
            return await list_available_miner(
                self.wandb,
                self.config,
                rent_status,
                page_size,
                page_number,
            )

        @self.app.post(
            "/list/allocated_hotkeys",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List available resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while fetch allocated hotkey from wandb",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_allocated_hotkeys_handler() -> JSONResponse:
            return await list_allocated_hotkeys(self.wandb, self.config)

        @self.app.post(
            "/test/notify",
            tags=["Testing"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "Notify allocation event testing is success",
                },
                400: {
                    "model": ErrorResponse,
                    "description": "Notify allocation event testing is failed",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def test_notify_handler(
            hotkey: str = None,
            uuid_key: str = None,
            event: str = None,
        ) -> JSONResponse:
            return await test_notify(
                _notify_allocation_status,
                self.deallocation_notify_url,
                self.status_notify_url,
                hotkey,
                uuid_key,
                event,
            )

        @self.app.post(
            "/list/associated_hotkeys",
            tags=["WandB"],
            response_model=SuccessResponse | ErrorResponse,
            responses={
                200: {
                    "model": SuccessResponse,
                    "description": "List available resources successfully.",
                },
                401: {"model": ErrorResponse, "description": "Missing authorization"},
                404: {
                    "model": ErrorResponse,
                    "description": "Error occurred while fetch associated hotkeys",
                },
                422: {
                    "model": ErrorResponse,
                    "description": "Validation Error, Please check the request body.",
                },
            },
        )
        async def list_associated_hotkeys_handler(
            netuid: Optional[int] = None,
            coldkey: Optional[str] = None,
            block: Optional[int] = None,
        ) -> JSONResponse:
            return await list_associated_hotkeys(
                self.subtensor,
                self.db,
                self.config,
                netuid,
                coldkey,
                block,
            )

    async def _refresh_metagraph(self):
        """
        Refresh the metagraph by resync_period.
        """
        while True:
            if self.metagraph:
                self.metagraph.sync(lite=True, subtensor=self.subtensor)
                # bt.logging.info("API: Metagraph refreshed")
                await asyncio.sleep(DATA_SYNC_PERIOD)

    # async def _refresh_allocation(self):
    #     """
    #     Refresh the allocation by resync_period.
    #     """
    #     while True:
    #         self.allocation_table = self.wandb.get_allocated_hotkeys([], False)
    #         bt.logging.info(f"API: Allocation refreshed: {self.allocation_table}")
    #         await asyncio.sleep(DATA_SYNC_PERIOD)

    async def _check_allocation(self):
        """
        Check the allocation by resync_period.
        """
        while True:
            cursor = self.db.get_cursor()
            try:
                # Retrieve all records from the allocation table
                cursor.execute("SELECT id, hotkey, details FROM allocation")
                rows = cursor.fetchall()
                for row in rows:
                    id, hotkey, details = row
                    info = json.loads(details)
                    uuid_key = info.get("uuid")
                    # Check if hotkey exists in self.metagraph.hotkeys and uuid_key is valid
                    if hotkey in self.metagraph.hotkeys and uuid_key:
                        index = self.metagraph.hotkeys.index(hotkey)
                        axon = self.metagraph.axons[index]

                        task = asyncio.create_task(self.dendrite_check(axon, Allocate(timeline=1, checking=True)))
                        try:
                            register_response = await asyncio.wait_for(task, timeout=10)
                        except asyncio.TimeoutError:
                            register_response = True # Handle timeout case appropriately

                        deallocated_at = datetime.now(timezone.utc)
                        if (isinstance(register_response, dict)
                                and "status" in register_response
                                and register_response.get("status") is False):
                            response = await _notify_allocation_status(
                                self.deallocation_notify_url,
                                self.status_notify_url,
                                event_time=deallocated_at,
                                hotkey=hotkey,
                                uuid=uuid_key,
                                event="ONLINE",
                                details=f"GPU Resume for {ALLOCATE_CHECK_PERIOD} seconds",
                            )
                            if hotkey in self.checking_allocated:
                                self.checking_allocated = [ x for x in self.checking_allocated if x != hotkey ]
                            bt.logging.info(
                                f"API: Allocation ONLINE notification for hotkey: {hotkey}"
                            )
                        else:
                            # handle the case when no response is received or the docker is not running
                            self.checking_allocated.append(hotkey)
                            # bt.logging.info(f"API: No response timeout is triggered for hotkey: {hotkey}")
                            deallocated_at = datetime.now(timezone.utc)
                            response = await _notify_allocation_status(
                                self.deallocation_notify_url,
                                self.status_notify_url,
                                event_time=deallocated_at,
                                hotkey=hotkey,
                                uuid=uuid_key,
                                event="OFFLINE",
                                details=f"No response timeout for {ALLOCATE_CHECK_PERIOD} seconds",
                            )
                            bt.logging.info(f"API: Allocation OFFLINE notification for hotkey: {hotkey}")
                            if not response:
                                pass
                        if self.checking_allocated.count(hotkey) >= ALLOCATE_CHECK_COUNT:
                            # update the allocation table
                            update_allocation_db(hotkey, info, False)
                            await _update_allocation_wandb(self.db, self.wandb)
                            response = await _notify_allocation_status(
                                self.deallocation_notify_url,
                                self.status_notify_url,
                                event_time=deallocated_at,
                                hotkey=hotkey,
                                uuid=uuid_key,
                                event="DEALLOCATION",
                                details=f"No response timeout for {ALLOCATE_CHECK_COUNT} times",
                            )
                            bt.logging.info(
                                f"API: deallocate event triggered due to {hotkey} "
                                f"is timeout for {ALLOCATE_CHECK_COUNT} times"
                            )
                            # remove the hotkey from checking table
                            if not response:
                                self.notify_retry_table.append(
                                    {
                                        "event_time": deallocated_at,
                                        "hotkey": hotkey,
                                        "uuid": uuid_key,
                                        "event": "DEALLOCATION",
                                        "details": "Retry deallocation notify event triggered",
                                    }
                                )
                for entry in self.notify_retry_table:
                    response = await _notify_allocation_status(
                        self.deallocation_notify_url,
                        self.status_notify_url,
                        event_time=entry["event_time"],
                        hotkey=entry["hotkey"],
                        uuid=entry["uuid"],
                        event=entry["event"],
                        details=entry["details"],
                    )
                    if response:
                        self.notify_retry_table.remove(entry)
                        bt.logging.info(
                            f"API: Notify {entry['event']} retry event is success on {entry['hotkey']} "
                        )
                    else:
                        bt.logging.info(
                            f"API: Notify {entry['event']} retry event is failed on {entry['hotkey']} "
                        )
            except Exception as e:
                bt.logging.error(f"API: Error occurred while checking allocation: {e}")
            finally:
                # bt.logging.info("API: Allocation checking triggered")
                await asyncio.sleep(ALLOCATE_CHECK_PERIOD)

    def run(self):
        """
        Run the FastAPI app.
        """
        cert_path = os.getenv("CERT_LOCATION")
        if (
            os.path.exists(cert_path + "/ca.cer")
            and os.path.exists(cert_path + "/server.key")
            and os.path.exists(cert_path + "/server.cer")
        ):
            uvicorn.run(
                self.app,
                host=self.ip_addr,
                port=self.port,
                log_level="critical",
                ssl_keyfile=cert_path + "/server.key",
                ssl_certfile=cert_path + "/server.cer",
                ssl_cert_reqs=DEFAULT_SSL_MODE,  # 1 for client CERT optional, 2 for client CERT_REQUIRED
                ssl_ca_certs=cert_path + "/ca.cer",
            )
        else:
            bt.logging.error(
                f"API: No SSL certificate found, please generate one with /{cert_path}/gen_ca.sh"
            )
            exit(1)

    def start(self):
        """
        Start the FastAPI app in the process.
        """
        self.process = multiprocessing.Process(
            target=self.run, args=(), daemon=True
        ).start()

    def stop(self):
        """
        Stop the FastAPI app in the process.
        """
        if self.process:
            self.process.terminate()
            self.process.join()
