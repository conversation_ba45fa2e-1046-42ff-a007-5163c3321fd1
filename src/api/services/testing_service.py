# Standard Library Imports
from datetime import datetime, timezone
import uuid

# Third-Party Imports
import <PERSON><PERSON> as bt
import urllib3
from urllib3.exceptions import InsecureRequestWarning
from fastapi import status
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any, Callable

# Disable SSL Warnings
urllib3.disable_warnings(InsecureRequestWarning)


async def test_notify(
    _notify_allocation_status: Callable[
        [str, str, str, str, str, str], Optional[Dict[str, Any]]
    ],
    deallocation_notify_url: str,
    status_notify_url: str,
    hotkey: str = None,
    uuid_key: str = None,
    event: str = None,
) -> JSONResponse:
    """
    This function is used to test the notification system.
    """
    try:
        if not hotkey:
            hotkey = "test_hotkey"
        if not uuid_key:
            uuid_key = str(uuid.uuid1())
        if not event:
            event = "DEALLOCATION"
        # Notify the allocation event
        response = await _notify_allocation_status(
            deallocation_notify_url,
            status_notify_url,
            event_time=datetime.now(timezone.utc),
            hotkey=hotkey,
            uuid=uuid_key,
            event=event,
        )
        if response:
            bt.logging.info("API: Notify allocation event testing is success")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "Notify allocation event testing is success",
                },
            )
        else:
            bt.logging.error("API: Notify allocation event testing is failed")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "message": "Notify allocation event testing is failed",
                },
            )
    except Exception as e:
        bt.logging.error(f"API: An error occurred while testing notify: {e}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": "Error occurred while testing notify",
                "err_detail": e.__repr__(),
            },
        )
