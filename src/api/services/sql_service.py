# Standard Library Imports
import urllib3
from urllib3.exceptions import InsecureRequestWarning
from ipwhois import IPWhois
from typing import Optional, Dict, Any, List

# Third-Party Imports
import bittensor as bt
from bittensor import Metagraph, StakeInfo
from fastapi import status
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.concurrency import run_in_threadpool

# Local Imports
from neurons.Validator.database.allocate import get_miner_details
from compute import (TRUSTED_VALIDATORS_HOTKEYS)
from compute.axon import ComputeSubnetSubtensor
from compute.utils.db import ComputeDb
from compute.wandb.wandb import ComputeWandb

from libs.register.allocate import get_allocations_from_db
from libs.register.resources import extract_miner_details
from api.models.register import Resource, ResourceQuery
from api.constants import PUBLIC_WANDB_ENTITY, PUBLIC_WANDB_NAME
from api.utils import _paginate_list


# Disable SSL Warnings
urllib3.disable_warnings(InsecureRequestWarning)


async def list_allocations(db, checking_allocated: List[str]) -> JSONResponse:
    """
    The list allocation API endpoint.
    The API will return the current allocation on the validator.
    """
    allocation_list: List[dict] = get_allocations_from_db(db, checking_allocated)
    if isinstance(allocation_list, dict) and "error" in allocation_list:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": "An error occurred while retrieving allocation details.",
                "err_detail": allocation_list["error"],
            },
        )
    if not allocation_list:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "No resources found.",
                "data": "No allocated resources found. Allocate a resource with validator.",
            },
        )
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "message": "List allocations successfully.",
            "data": allocation_list,
        },
    )


async def list_resources(
    db: ComputeDb,
    config: Dict[str, Any],
    wandb: ComputeWandb,
    metagraph: Metagraph,
    query: ResourceQuery = None,
    stats: bool = False,
    page_size: Optional[int] = None,
    page_number: Optional[int] = None,
) -> JSONResponse:
    """
    The list resources API endpoint.
    The API will return the current miner resource and their detail specs on the validator.
    query: The query parameter to filter the resources.
    """
    specs_details = await run_in_threadpool(get_miner_details, db)
    bt.logging.info("API: List resources on compute subnet")
    # check wandb for available hotkeys
    # self.wandb.api.flush()
    running_hotkey = []
    filter_rule = {
        "$and": [
            {"config.config.netuid": config.netuid},
            {"config.role": "miner"},
            {"state": "running"},
        ]
    }
    runs = await run_in_threadpool(
        wandb.api.runs, f"{PUBLIC_WANDB_ENTITY}/{PUBLIC_WANDB_NAME}", filter_rule
    )
    for run in runs:
        run_config = run.config
        run_hotkey = run_config.get("hotkey")
        running_hotkey.append(run_hotkey)
    # Initialize a dictionary to keep track of GPU instances
    resource_list = []
    gpu_instances = {}
    total_gpu_counts = {}
    # Get the allocated hotkeys from wandb
    allocated_hotkeys = await run_in_threadpool(wandb.get_allocated_hotkeys, TRUSTED_VALIDATORS_HOTKEYS, False)
    if specs_details:
        # Iterate through the miner specs details and print the table
        for hotkey, details in specs_details.items():
            if hotkey in running_hotkey:
                miner_details = extract_miner_details(
                    hotkey, details, gpu_instances, total_gpu_counts, allocated_hotkeys
                )
                if "gpu_name" in miner_details:
                    gpu_name = miner_details["gpu_name"]
                else:
                    gpu_name = None
                if "gpu_capacity" in miner_details:
                    gpu_capacity = miner_details["gpu_capacity"]
                else:
                    gpu_capacity = None
                if "gpu_count" in miner_details:
                    gpu_count = miner_details["gpu_count"]
                else:
                    gpu_count = None
                if "cpu_count" in miner_details:
                    cpu_count = miner_details["cpu_count"]
                else:
                    cpu_count = None
                if "ram" in miner_details:
                    ram = miner_details["ram"]
                else:
                    ram = None
                if "hard_disk" in miner_details:
                    hard_disk = miner_details["hard_disk"]
                else:
                    hard_disk = None
                if "status" in miner_details:
                    allocate_status = miner_details["status"]
                else:
                    allocate_status = None
                resource = Resource()
                add_resource = False
                # Print the row with column separators
                resource.hotkey = hotkey
                try:
                    if (
                        gpu_name != "Invalid details"
                        and gpu_name != "No details available"
                    ):
                        if query is None or query == {}:
                            add_resource = True
                        else:
                            if (
                                query.gpu_name is not None
                                and query.gpu_name.lower() not in gpu_name
                            ):
                                continue
                            if (
                                query.gpu_capacity_max is not None
                                and float(gpu_capacity) > query.gpu_capacity_max
                            ):
                                continue
                            if (
                                query.gpu_capacity_min is not None
                                and float(gpu_capacity) < query.gpu_capacity_min
                            ):
                                continue
                            if (
                                query.cpu_count_max is not None
                                and int(cpu_count) > query.cpu_count_max
                            ):
                                continue
                            if (
                                query.cpu_count_min is not None
                                and int(cpu_count) < query.cpu_count_min
                            ):
                                continue
                            if (
                                query.ram_total_max is not None
                                and float(ram) > query.ram_total_max
                            ):
                                continue
                            if (
                                query.ram_total_min is not None
                                and float(ram) < query.ram_total_min
                            ):
                                continue
                            if (
                                query.hard_disk_total_max is not None
                                and float(hard_disk) > query.hard_disk_total_max
                            ):
                                continue
                            if (
                                query.hard_disk_total_min is not None
                                and float(hard_disk) < query.hard_disk_total_min
                            ):
                                continue
                            add_resource = True
                        if add_resource:
                            resource.cpu_count = int(cpu_count)
                            resource.gpu_name = gpu_name
                            resource.gpu_capacity = float(gpu_capacity)
                            resource.gpu_count = int(gpu_count)
                            resource.ram = float(ram)
                            resource.hard_disk = float(hard_disk)
                            resource.allocate_status = allocate_status
                            resource_list.append(resource)
                except (KeyError, IndexError, TypeError, ValueError, Exception) as e:
                    bt.logging.error(
                        f"API: Error occurred while filtering resources: {e}"
                    )
                    continue
        if stats:
            status_counts = {"available": 0, "reserved": 0, "total": 0}
            try:
                for item in resource_list:
                    status_code = item.dict()["allocate_status"]
                    if status_code in status_counts:
                        status_counts[status_code] += 1
                        status_counts["total"] += 1
            except Exception as e:
                bt.logging.error(f"API: Error occurred while counting status: {e}")
                status_counts = {"available": 0, "reserved": 0, "total": 0}
            bt.logging.info("API: List resources successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List resources successfully",
                    "data": jsonable_encoder({"stats": status_counts}),
                },
            )
        else:
            if page_number:
                page_size = page_size if page_size else 50
                result = _paginate_list(resource_list, page_number, page_size)
            else:
                result = {
                    "page_items": resource_list,
                    "page_number": 1,
                    "page_size": len(resource_list),
                    "next_page_number": None,
                }
            bt.logging.info("API: List resources successfully")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List resources successfully",
                    "data": jsonable_encoder(result),
                },
            )
    else:
        bt.logging.info("API: There is no resource available")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "There is no resource available",
                "err_detail": "No resources found.",
            },
        )


async def map_axon_ip_to_resources(
    metagraph: Metagraph, resources: List[Resource]
) -> List[Resource]:
    """
    Map axon IPs to the list of resources based on hotkeys.
    """
    for resource in resources:
        hotkey = resource.hotkey
        if hotkey:
            for axon in metagraph.axons:
                if axon.hotkey == hotkey:
                    resource.ip = axon.ip
                    print("********1",resource.ip)
                    obj = IPWhois(resource.ip)
                    result = obj.lookup_rdap()
                    print("********2",result)
                    resource.geo = result.get("asn_country_code", "Unknown")
                    break
    return resources


async def list_associated_hotkeys(
    subtensor: ComputeSubnetSubtensor,
    db: ComputeDb,
    config: Dict[str, Any],
    netuid: Optional[int] = None,
    coldkey: Optional[str] = None,
    block: Optional[int] = None,
) -> JSONResponse:
    """
    This function gets all associated hotkeys from all validators.
    Only relevant for validators.
    """
    try:
        result = subtensor.query_runtime_api(
            runtime_api="StakeInfoRuntimeApi",
            method="get_stake_info_for_coldkey",
            params=[coldkey],
            block=block,
        )

        if result is None:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List associated hotkeys",
                    "data": jsonable_encoder([]),
                },
            )

        netuid = netuid or config.netuid
        stakes = StakeInfo.list_from_dicts(result)  # type: ignore
        hokeys = {stake.hotkey_ss58 for stake in stakes if stake.netuid == netuid}
        if not hokeys:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "List associated hotkeys",
                    "data": jsonable_encoder([]),
                },
            )

        # Get the allocated hotkeys
        result = [
            allocation
            for allocation in get_allocations_from_db(db, [])
            if allocation["hotkey"] in hokeys
        ]
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "List allocated hotkeys",
                "data": jsonable_encoder(result),
            },
        )
    except Exception as e:
        bt.logging.error("API: list_allocated_hotkeys error with %s", e)
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "message": "Error occurred while fetching allocated hotkey from wandb",
                "err_detail": str(e),
            },
        )
