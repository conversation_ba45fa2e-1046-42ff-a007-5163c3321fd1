import pytest
import json
from unittest.mock import MagicMock
from fastapi.responses import JSONResponse

from api.services.sql_service import list_associated_hotkeys

# Mock the bittensor StakeInfo class
class MockStakeInfo:
    def __init__(self, netuid, hotkey_ss58):
        self.netuid = netuid
        self.hotkey_ss58 = hotkey_ss58

    @classmethod
    def list_from_dicts(cls, data):
        """Mock implementation of StakeInfo.list_from_dicts"""
        if not data:
            return []

        stakes = []
        for item in data:
            stakes.append(cls(
                netuid=item.get('netuid', 15),
                hotkey_ss58=item.get('hotkey_ss58', 'mock_hotkey')
            ))
        return stakes

# Mock the run_in_threadpool function
async def mock_run_in_threadpool(func, *args, **kwargs):
    """Mock implementation of run_in_threadpool"""
    return func(*args, **kwargs)

class TestListAssociatedHotkeys:
    """Test cases for list_associated_hotkeys_handler function"""

    @pytest.fixture
    def mock_subtensor(self):
        """Mock ComputeSubnetSubtensor"""
        mock = MagicMock()
        mock.query_runtime_api = MagicMock()
        return mock

    @pytest.fixture
    def mock_db(self):
        """Mock ComputeDb"""
        mock = MagicMock()
        mock.get_allocated_hotkeys = MagicMock()
        return mock

    @pytest.fixture
    def mock_config(self):
        """Mock config dictionary"""
        return {"netuid": 15}

    @pytest.mark.asyncio
    async def test_list_associated_hotkeys_success_with_hotkeys(self, mock_subtensor, mock_db, mock_config):
        """Test successful response with associated hotkeys"""
        # Mock the subtensor query response
        mock_subtensor.query_runtime_api.return_value = [
            {"netuid": 15, "hotkey_ss58": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"},
            {"netuid": 15, "hotkey_ss58": "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW"},
            {"netuid": 16, "hotkey_ss58": "5SomeOtherHotkeyForDifferentNetuid"}  # Different netuid, should be filtered
        ]

        result = await list_associated_hotkeys(
            subtensor=mock_subtensor,
            db=mock_db,
            config=mock_config,
            netuid=15,
            coldkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
            block=None,
        )

        # Verify the result
        assert isinstance(result, JSONResponse)
        assert result.status_code == 200

        # Parse the response content
        content = result.body.decode()
        response_data = json.loads(content)

        assert response_data["success"] is True
        assert response_data["message"] == "List associated hotkeys"
        assert len(response_data["data"]) == 2
        assert "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR" in response_data["data"]
        assert "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW" in response_data["data"]

    @pytest.mark.asyncio
    async def test_list_associated_hotkeys_empty_result(self, mock_subtensor, mock_db, mock_config):
        """Test response when no stake info is returned"""
        # Mock empty response from subtensor
        mock_subtensor.query_runtime_api.return_value = None

        result = await list_associated_hotkeys(
            subtensor=mock_subtensor,
            db=mock_db,
            config=mock_config,
            coldkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        )

        # Verify the result
        assert isinstance(result, JSONResponse)
        assert result.status_code == 200

        content = result.body.decode()
        response_data = json.loads(content)

        assert response_data["success"] is True
        assert response_data["message"] == "List associated hotkeys"
        assert response_data["data"] == []

    @pytest.mark.asyncio
    async def test_list_associated_hotkeys_with_allocated_filter(self, mock_subtensor, mock_db, mock_config):
        """Test response when only_show_allocated is True"""
        # Mock the subtensor query response
        mock_subtensor.query_runtime_api.return_value = [
            {"netuid": 15, "hotkey_ss58": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"},
            {"netuid": 15, "hotkey_ss58": "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW"}
        ]

        # Mock allocated hotkeys from wandb
        allocated_hotkeys = ["5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"]
        mock_db.get_allocated_hotkeys.return_value = allocated_hotkeys

        # Use the local function instead of trying to import from source
        result = await list_associated_hotkeys(
            subtensor=mock_subtensor,
            db=mock_db,
            config=mock_config,
            netuid=15,
            coldkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
        )

        # Verify the result
        assert isinstance(result, JSONResponse)
        assert result.status_code == 200

        content = result.body.decode()
        response_data = json.loads(content)

        assert response_data["success"] is True
        assert response_data["message"] == "List allocated hotkeys"
        # Should only return the intersection of associated and allocated hotkeys
        assert len(response_data["data"]) == 1
        assert "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR" in response_data["data"]

    @pytest.mark.asyncio
    async def test_list_associated_hotkeys_different_netuid(self, mock_subtensor, mock_db, mock_config):
        """Test filtering by different netuid"""
        # Mock the subtensor query response with mixed netuids
        mock_subtensor.query_runtime_api.return_value = [
            {"netuid": 15, "hotkey_ss58": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"},
            {"netuid": 16, "hotkey_ss58": "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW"},
            {"netuid": 16, "hotkey_ss58": "5AnotherHotkeyForNetuid16"}
        ]

        # Use the local function instead of trying to import from source
        result = await list_associated_hotkeys(
            subtensor=mock_subtensor,
            db=mock_db,
            config=mock_config,
            netuid=16,  # Filter for netuid 16
            coldkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        )

        # Verify the result
        assert isinstance(result, JSONResponse)
        assert result.status_code == 200

        content = result.body.decode()
        response_data = json.loads(content)

        assert response_data["success"] is True
        assert response_data["message"] == "List associated hotkeys"
        assert len(response_data["data"]) == 2
        assert "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW" in response_data["data"]
        assert "5AnotherHotkeyForNetuid16" in response_data["data"]

    @pytest.mark.asyncio
    async def test_list_associated_hotkeys_no_matching_netuid(self, mock_subtensor, mock_db, mock_config):
        """Test when no hotkeys match the specified netuid"""
        # Mock the subtensor query response with different netuids
        mock_subtensor.query_runtime_api.return_value = [
            {"netuid": 16, "hotkey_ss58": "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW"},
            {"netuid": 17, "hotkey_ss58": "5AnotherHotkeyForNetuid17"}
        ]

        # Use the local function instead of trying to import from source
        result = await list_associated_hotkeys(
            subtensor=mock_subtensor,
            db=mock_db,
            config=mock_config,
            netuid=15,  # Filter for netuid 15, but no hotkeys match
            coldkey="5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"
        )

        # Verify the result
        assert isinstance(result, JSONResponse)
        assert result.status_code == 200

        content = result.body.decode()
        response_data = json.loads(content)

        assert response_data["success"] is True
        assert response_data["message"] == "List associated hotkeys"
        assert response_data["data"] == []
