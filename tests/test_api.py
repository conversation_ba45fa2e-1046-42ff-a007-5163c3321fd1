import os
import pytest
import httpx
import allure
from unittest.mock import MagicMock


class TestAPI:
    uuid = ""
    cert_path = os.getenv("CERT_LOCATION")
    if cert_path is None:
        raise ValueError("CERT_LOCATION environment variable is not set.")
    cert_path = os.path.abspath(cert_path)
    cert = (
        os.path.join(cert_path, "client.crt"),
        os.path.join(cert_path, "client.key"),
    )
    ca = os.path.join(cert_path, "ca.cer")
    url = "https://192.168.0.11:8093"

    def create_mock_response(self, status_code=200, data=None):
        mock_response = MagicMock()
        mock_response.status_code = status_code
        mock_response.json.return_value = data
        return mock_response

    @pytest.fixture
    def mock_httpx_client(self, mocker):
        mock_client = mocker.patch("httpx.Client")
        mock_instance = MagicMock()

        # Default mock response data
        default_data = {
            "message": "Welcome to Compute Subnet Allocation API, Please access the API via endpoint."
        }
        mock_read_root_response = self.create_mock_response(data=default_data)
        mock_instance.get.return_value = mock_read_root_response

        # Case 1 - List all runs
        mock_list_all_runs_case1_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List run resources successfully.",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "id": "y0wfowqh",
                                "name": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "description": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "state": "running",
                                "start_at": "2024-04-25T11:03:02Z",
                            }
                        },
                        {
                            "2": {
                                "id": "y3i9mek1",
                                "name": "validator-5CMCfpxEb1BZKaCiKqt9CqogzTVchQYgDTqCc1s5qpMDorhW",
                                "description": "validator-5CMCfpxEb1BZKaCiKqt9CqogzTVchQYgDTqCc1s5qpMDorhW",
                                "state": "running",
                                "start_at": "2025-01-21T13:05:53Z",
                            }
                        }
                    ],
                    "page_number": 1,
                    "page_size": 2,
                    "next_page_number": None,
                },
            }
        )

        # Case 2 - List all runs (Test list all runs endpoint with hotkey)
        mock_list_all_runs_case2_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List run resources successfully.",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "id": "y0wfowqh",
                                "name": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "description": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "state": "running",
                                "start_at": "2024-04-25T11:03:02Z",
                            }
                        },
                    ],
                    "page_number": 1,
                    "page_size": 1,
                    "next_page_number": None,
                },
            }
        )

        # Case 3 - List all runs (Pagination example)
        mock_list_all_runs_case3_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List run resources successfully.",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "id": "y0wfowqh",
                                "name": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "description": "validator-5Eo33v5tbEBCnmnP8iFXrpaqvX4cL7GFbqxMbYB9ddj7axws",
                                "state": "running",
                                "start_at": "2024-04-25T11:03:02Z",
                            }
                        }
                    ],
                    "page_number": 1,
                    "page_size": 1,
                    "next_page_number": 2,
                },
            }
        )

        # Case 4 - List specs
        mock_list_specs_case1_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List specs successfully",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "hotkey": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                                "configs": {"netuid": 15},
                                "specs": {
                                    "cpu": {"count": 24},
                                    "gpu": {"count": 1},
                                    "ram": {"free": 166535036928},
                                    "hard_disk": {"free": 1688055873536},
                                    "has_docker": True,
                                },
                                "state": "running",
                            }
                        },
                        {
                            "2": {
                                "hotkey": "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW",
                                "configs": {"netuid": 15},
                                "specs": {
                                    "cpu": {"count": 2},
                                    "gpu": {"count": 1},
                                    "ram": {"free": 8644440064},
                                    "hard_disk": {"free": 74092494848},
                                    "has_docker": True,
                                },
                                "state": "running",
                            }
                        },
                    ],
                    "page_number": 1,
                    "page_size": 2,
                    "next_page_number": None,
                },
            }
        )

        # Case 5 - List specs (Test list all specs endpoint with hotkey)
        mock_list_specs_case2_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List specs successfully",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "hotkey": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                                "configs": {"netuid": 15},
                                "specs": {
                                    "cpu": {"count": 24},
                                    "gpu": {"count": 1},
                                    "ram": {"free": 166535036928},
                                    "hard_disk": {"free": 1688055873536},
                                    "has_docker": True,
                                },
                                "state": "running",
                            }
                        },
                    ],
                    "page_number": 1,
                    "page_size": 1,
                    "next_page_number": None,
                },
            }
        )

        # Case 6 - List specs (Pagination example)
        mock_list_specs_case3_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List specs successfully",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "hotkey": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                                "configs": {"netuid": 15},
                                "specs": {
                                    "cpu": {"count": 24},
                                    "gpu": {"count": 1},
                                    "ram": {"free": 166535036928},
                                    "hard_disk": {"free": 1688055873536},
                                    "has_docker": True,
                                },
                                "state": "running",
                            }
                        },
                    ],
                    "page_number": 1,
                    "page_size": 1,
                    "next_page_number": 2,
                },
            }
        )

        # Case 7 - List run_by_name (success)
        mock_list_run_by_name_success_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List run by name",
                "data": {
                    "page_items": [
                        {
                            "1": {
                                "id": "1hpz683y",
                                "name": "miner-5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                                "description": "miner-5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                                "configs": {
                                    "netuid": True,
                                    "axon.ip": True,
                                    "axon.port": True,
                                    "axon.external_ip": True,
                                    "subtensor.network": True,
                                    "axon.external_port": True,
                                }
                            }
                        },
                    ],
                    "page_number": 1,
                    "page_size": 1,
                    "next_page_number": 2,
                },
            }
        )

        # Case 8 - List run_by_name (failure)
        mock_list_run_by_name_failure_response = self.create_mock_response(
            data={
                        "success": True,
                        "message": "No run available",
                        "data": {}
            }
        )

        # Case 9 - List associated hotkeys (success with hotkeys)
        mock_list_associated_hotkeys_success_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List associated hotkeys",
                "data": [
                    "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR",
                    "5DHe1a3tUhB9fo8hBAzpFcYEHrq8QiYvkCcGvjjRe9sHpjwW"
                ]
            }
        )

        # Case 10 - List associated hotkeys (success with allocated hotkeys only)
        mock_list_associated_hotkeys_allocated_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List allocated hotkeys",
                "data": [
                    "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"
                ]
            }
        )

        # Case 11 - List associated hotkeys (empty result)
        mock_list_associated_hotkeys_empty_response = self.create_mock_response(
            data={
                "success": True,
                "message": "List associated hotkeys",
                "data": []
            }
        )

        # Mocking the behavior of the HTTP client
        def mock_post(url):
            if "hotkey=" in url and "/list/associated_hotkeys" not in url:
                if "/list/all_runs" in url:
                    return mock_list_all_runs_case2_response
                if "/list/specs" in url:
                    return mock_list_specs_case2_response
            elif "page_size" in url and "page_number" in url:
                if "/list/all_runs" in url:
                    return mock_list_all_runs_case3_response
                if "/list/specs" in url:
                    return mock_list_specs_case3_response
            elif "run_name" in url:
                if "miner-5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR" in url:
                    return mock_list_run_by_name_success_response
                else:
                    return mock_list_run_by_name_failure_response
            elif "/list/associated_hotkeys" in url:
                if "only_show_allocated=True" in url:
                    return mock_list_associated_hotkeys_allocated_response
                elif "coldkey=" in url:
                    return mock_list_associated_hotkeys_success_response
                else:
                    return mock_list_associated_hotkeys_empty_response
            else:
                if "/list/all_runs" in url:
                    return mock_list_all_runs_case1_response
                if "/list/specs" in url:
                    return mock_list_specs_case1_response

        mock_instance.post.side_effect = mock_post
        mock_client.return_value.__enter__.return_value = mock_instance
        return mock_client

    @allure.testcase("TC-1: Test / endpoint")
    def test_read_root(self, mock_httpx_client):
        """
        Test / endpoint with mocked httpx client
        """
        with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
            response = client.get(self.url)

        assert response.status_code == 200
        assert response.json() == {
            "message": "Welcome to Compute Subnet Allocation API, Please access the API via endpoint."
        }

    # List - wandb
    @pytest.mark.parametrize(
        "params, test_case",
        [
            ({}, "TC-2-1: Test list all runs endpoint"),
            (
                {"hotkey": "5DZeTenJNww4X1j6kPVeaxLX4Xp2q3Ahj61BXtTkCsyRr4zP"},
                "TC-2-2: Test list all runs endpoint with hotkey",
            ),
            (
                {"page_size": 1, "page_number": 1},
                "TC-2-3: Test list all runs endpoint with pagination",
            ),
        ],
    )
    @allure.testcase("Test /list/all_runs endpoint")
    def test_list_all_runs(self, params, test_case, mock_httpx_client):
        """
        Test /list/all_runs with different parameters
        """
        url = self.url + "/list/all_runs"

        # If a parameter is provided, add it to the query.
        if params:
            url += "?" + "&".join([f"{key}={value}" for key, value in params.items()])

        with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
            response = client.post(url)

        assert response.status_code == 200
        resp_body = response.json()

        # Adjusting the assertion based on the response structure
        assert resp_body["success"] is True
        assert resp_body["message"] == "List run resources successfully."

        # Checking if the data field exists
        assert "data" in resp_body
        assert "page_items" in resp_body["data"]

        # Checking the length of the page_items
        page_items = resp_body["data"]["page_items"]
        assert len(page_items) > 0

        # Loop through the page_items and check if they contain the expected fields
        for item_dict in page_items:
            # Each item is a dictionary with a numeric string as the key
            for item in item_dict.values():
                assert "id" in item
                assert "name" in item
                assert "description" in item
                assert "state" in item
                assert "start_at" in item

    @pytest.mark.parametrize(
        "params, test_case",
        [
            ({}, "TC-3-1: Test list all specs endpoint"),  # Test without hotkey
            ({"hotkey": "5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"}, "TC-3-2: Test list all specs endpoint with hotkey"),  # Test with hotkey
            ({"page_size": 1, "page_number": 1}, "TC-3-3: Test list all specs endpoint with pagination")  # Test with pagination
        ]
    )
    @allure.testcase("TC-3: Test list specs endpoint with different scenarios")
    def test_list_specs(self, params, test_case, mock_httpx_client):
        """
        Test /list/specs with different parameters
        """
        # Construct the URL with query parameters
        url = self.url + "/list/specs"
        if params:
            url += "?" + "&".join([f"{key}={value}" for key, value in params.items()])

        with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
            response = client.post(url)

        assert response.status_code == 200
        resp_body = response.json()

        # Adjusting the assertion based on the response structure
        assert resp_body["success"] is True
        assert resp_body["message"] == "List specs successfully"

        # Checking if the data field exists
        assert "data" in resp_body
        assert "page_items" in resp_body["data"]

        # Checking the length of the page_items
        page_items = resp_body["data"]["page_items"]
        assert len(page_items) > 0

        # Loop through the page_items and check if they contain the expected fields
        for item_dict in page_items:
            # Each item is a dictionary with a numeric string as the key
            for item in item_dict.values():
                assert "hotkey" in item
                assert "configs" in item
                assert "specs" in item

                # Check "configs", "specs" and other nested dictionary keys
                for key, subitem in item.items():
                    if key == "configs" or key == "specs":
                        # Check if subitem is a dictionary and contains expected keys
                        if isinstance(subitem, dict):
                            if key == "configs":
                                assert "netuid" in subitem
                            elif key == "specs":
                                assert "cpu" in subitem
                                assert "gpu" in subitem
                                assert "ram" in subitem
                                assert "has_docker" in subitem

    @pytest.mark.parametrize(
        "params, expected_message, test_case",
        [
            ({"run_name":"miner-5F1Qro8uzhKjocjnKSSrLZnTaT8G6oxs67ymgjWQu6xbraPR"}, "List run by name", "Pass scenario"),  # Pass scenario
            ({"run_name":"miner"}, "No run available", "Fail scenario")  # Fail scenario
        ]
    )
    @allure.testcase("TC-4: Test list runs by name endpoint with different scenarios")
    def test_list_run_by_name(self, params, expected_message, test_case, mock_httpx_client):
        """
        Test /list/run_by_name with different parameters
        """
        if params:
            url=self.url + f"/list/run_by_name" + "?" + "&".join([f"{key}={value}" for key, value in params.items()])

        with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
            response = client.post(url)
        assert response.status_code == 200
        resp_body = response.json()

        assert resp_body["success"] == True
        assert resp_body["message"] == expected_message
        if expected_message == "List run by name":
            # Checking the length of the page_items
            page_items = resp_body["data"]["page_items"]
            assert len(page_items) > 0
            # Loop through the page_items and check if they contain the expected fields
            for item_dict in page_items:
                # Each item is a dictionary with a numeric string as the key
                for item in item_dict.values():
                    assert "id" in item
                    assert "name" in item
                    assert "description" in item
                    assert "configs" in item
                    # Check "configs", "specs" and other nested dictionary keys
                    for key, subitem in item.items():
                        if key == "configs":
                            # Check if subitem is a dictionary and contains expected keys
                            if isinstance(subitem, dict):
                                assert "netuid" in subitem
                                assert "axon.ip" in subitem
                                assert "axon.port" in subitem
                                assert "axon.external_ip" in subitem
                                assert "subtensor.network" in subitem
                                assert "axon.external_port" in subitem

    @pytest.mark.parametrize(
        "params, expected_message, test_case",
        [
            ({}, "List associated hotkeys", "TC-5-1: Test list associated hotkeys endpoint - no parameters"),
            ({"coldkey": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY"}, "List associated hotkeys", "TC-5-2: Test list associated hotkeys endpoint with coldkey"),
            ({"netuid": 27}, "List associated hotkeys", "TC-5-3: Test list associated hotkeys endpoint with netuid"),
            ({"only_show_allocated": True}, "List allocated hotkeys", "TC-5-4: Test list associated hotkeys endpoint with only_show_allocated"),
            ({"coldkey": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY", "netuid": 15}, "List associated hotkeys", "TC-5-5: Test list associated hotkeys endpoint with coldkey and netuid"),
        ]
    )
    @allure.testcase("TC-5: Test list associated hotkeys endpoint with different scenarios")
    def test_list_associated_hotkeys(self, params, expected_message, test_case, mock_httpx_client):
        """
        Test /list/associated_hotkeys with different parameters
        """
        # Construct the URL with query parameters
        url = self.url + "/list/associated_hotkeys"
        if params:
            url += "?" + "&".join([f"{key}={value}" for key, value in params.items()])

        with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
            response = client.post(url)

        assert response.status_code == 200
        resp_body = response.json()

        # Verify the response structure
        assert resp_body["success"] is True
        assert resp_body["message"] == expected_message

        # Checking if the data field exists
        assert "data" in resp_body
        assert isinstance(resp_body["data"], list)

        # For successful cases with data, verify the hotkey format
        if resp_body["data"]:
            for hotkey in resp_body["data"]:
                assert isinstance(hotkey, str)
                # Verify hotkey format (should be a valid SS58 address)
                assert len(hotkey) > 40  # SS58 addresses are typically 47-48 characters

    # @pytest.mark.parametrize("rent_status, page_size, page_number, expected_message, test_case", [
    #     (True, None, None, "List rented miners", "TC-5-1: Test list available endpoint - rent on scenario"),
    #     (False, None, None, "List available miners", "TC-5-2: Test list available endpoint - rent off scenario"),
    #     (None, 2, 1, "List available miners", "TC-5-3: Test list available endpoint with pagination"),
    # ])
    # @allure.testcase("TC-5: Test list available endpoint")
    # def test_list_available(self, rent_status, page_size, page_number, expected_message, test_case):
    #     """
    #     Test /list/available with different scenarios
    #     """
    #     url = self.url + "/list/available"
    #     params = {}

    #     if rent_status is not None:
    #         params["rent_status"] = str(rent_status).lower()  # Convert boolean to string "true" or "false"

    #     if page_size:
    #         params["page_size"] = page_size

    #     if page_number:
    #         params["page_number"] = page_number

    #     # Add parameters to the URL
    #     if params:
    #         url += "?" + "&".join([f"{key}={value}" for key, value in params.items()])

    #     with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
    #         response = client.post(url)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == expected_message
    #     assert len(resp_body["data"]) > 0 if rent_status is not False else True

    # @allure.testcase("TC-6: Test list allocated hotkeys endpoint")
    # def test_list_allocated_hotkeys(self):
    #     """
    #     Test /list/allocated_hotkeys
    #     """
    #     url=self.url + "/list/allocated_hotkeys"

    #     with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
    #         response = client.post(url)
    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     if len(resp_body["data"]) > 0:
    #         assert resp_body["message"] == "List allocated hotkeys"
    #     elif len(resp_body["data"]) == 0:
    #         assert (
    #                 resp_body["message"]
    #                 == "No validator with allocated info in the project opencompute."
    #         )
    #         assert resp_body["data"] == {}
    # # List - sqlite
    # @allure.testcase("TC-7: Test list allocations endpoint")
    # def test_list_allocations_sql(self):
    #     """
    #     Test /list/allocations_sql
    #     """
    #     url=self.url + "/list/allocations_sql"

    #     with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
    #         response = client.post(url)
    #     if response.status_code == 200:
    #         assert response.status_code == 200
    #         resp_body = response.json()
    #         assert resp_body["success"] == True
    #         if len(resp_body["data"]) > 0:
    #             assert resp_body["message"] == "List allocations successfully."
    #     else:
    #         assert response.status_code == 404
    #         resp_body = response.json()
    #         assert resp_body["success"] == False
    #         assert resp_body["message"] == "No resources found."

    # def send_post_request(self, endpoint, json_data=None, query_params=None):
    #     """Helper function to send post requests"""
    #     url = self.url + endpoint
    #     if query_params:
    #         url += "?" + "&".join([f"{key}={value}" for key, value in query_params.items()])

    #     with httpx.Client(cert=self.cert, verify=False, timeout=10) as client:
    #         response = client.post(url, headers={"Content-Type": "application/json", "Accept": "application/json"}, json=json_data)
    #     return response

    # @pytest.mark.parametrize("gpu_name, cpu_count_min, expected_message, expected_data", [
    #     ("4090", 1, "List resources successfully", "data exists"),
    #     ("h2000", 1, "List resources successfully", "empty data"),
    # ])
    # @allure.testcase("TC-8-1: Test list resource sql endpoint")
    # def test_list_resource_sql(self, gpu_name, cpu_count_min, expected_message, expected_data):
    #     """Test /list/resources_sql"""
    #     json_data = {"gpu_name": gpu_name, "cpu_count_min": cpu_count_min}
    #     response = self.send_post_request("/list/resources_sql", json_data)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == expected_message
    #     if expected_data == "data exists":
    #         assert len(resp_body["data"]) > 0
    #     else:
    #         assert resp_body["data"] == {'page_items': [], 'page_number': 1, 'page_size': 0, 'next_page_number': None}

    # @pytest.mark.parametrize("stats, page_size, page_number", [
    #     (False, 2, 1),
    #     (True, 2, 1),
    # ])
    # @allure.testcase("TC-8-2: Test list resource sql endpoint with stats and pagination")
    # def test_list_resource_sql_with_stats_and_pagination(self, stats, page_size, page_number):
    #     """Test /list/resources_sql with stats and pagination"""
    #     query_params = {"stats": str(stats).lower(), "page_size": page_size, "page_number": page_number}
    #     response = self.send_post_request("/list/resources_sql", query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "List resources successfully"

    # # Allocation
    # @allure.testcase("TC-9: Test allocation spec endpoint")
    # def test_service_allocate_spec(self):
    #     """
    #     Test /service/allocate_spec
    #     """
    #     json_data = {
    #         "requirements": {
    #             "cpu_count": 8,
    #             "gpu_type": "NVIDIA GeForce GTX 1660 Ti with Max-Q Design",
    #             "gpu_size": 1,
    #             "ram": 1,
    #             "hard_disk": 1,
    #             "timeline": 90
    #         },
    #         "docker_requirement": {
    #             "base_image": "ubuntu",
    #             "ssh_key": "",
    #             "volume_path": "/tmp",
    #             "dockerfile": ""
    #         }
    #     }
    #     response = self.send_post_request("/service/allocate_spec", json_data)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource was successfully allocated"
    #     assert resp_body["data"]["resource"] != ""
    #     TestAPI.uuid = resp_body["data"]["uuid_key"]

    # @allure.testcase("TC-10-1: Test deallocation spec endpoint")
    # @pytest.mark.depends(on=["test_service_allocate_spec"])
    # def test_service_deallocation_spec(self):
    #     """
    #     Test /service/deallocate
    #     """
    #     query_params = {"hotkey": self.hotkey, "uuid_key": TestAPI.uuid}
    #     response = self.send_post_request("/service/deallocate", query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource deallocated successfully."

    # @allure.testcase("TC-10-2: Test deallocation spec with not found endpoint")
    # def test_service_deallocation_error(self):
    #     """
    #     Test /service/deallocate
    #     """
    #     query_params = {"hotkey": "miner-aaa", "uuid_key": "1234"}
    #     response = self.send_post_request("/service/deallocate", query_params=query_params)

    #     assert response.status_code == 404
    #     resp_body = response.json()
    #     assert resp_body["success"] == False
    #     assert resp_body["message"] == "No allocation details found for the provided hotkey."

    # @allure.testcase("TC-11-1: Test allocation hotkey endpoint")
    # @pytest.mark.depends(on=["test_service_deallocation_spec"])
    # def test_service_allocate_hotkey(self):
    #     """Test /service/allocate_hotkey"""
    #     json_data = {
    #         "base_image": "ubuntu",
    #         "ssh_key": "",
    #         "ssh_port": 4444,
    #         "volume_path": "/tmp",
    #         "dockerfile": ""
    #     }
    #     query_params = {"hotkey": self.hotkey}
    #     response = self.send_post_request("/service/allocate_hotkey", json_data=json_data, query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource was successfully allocated"
    #     assert resp_body["data"]["resource"] != ""
    #     TestAPI.uuid = resp_body["data"]["uuid_key"]

    # @allure.testcase("TC-11-2: Test allocation miner status")
    # @pytest.mark.depends(on=["test_service_allocate_hotkey"])
    # def test_service_check_miner_status(self):
    #     """Test /service/check_miner_status"""
    #     response = self.send_post_request(
    #         "/service/check_miner_status",
    #         json_data=[self.hotkey, "test_miner"]
    #     )

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "List hotkey status successfully."
    #     assert len(resp_body["data"]) > 0
    #     assert resp_body["data"][0]["hotkey"] == self.hotkey
    #     assert resp_body["data"][0]["status"] == "Docker ONLINE"
    #     assert resp_body["data"][1]["hotkey"] == "test_miner"
    #     assert resp_body["data"][1]["status"] == "Not Found"

    # @allure.testcase("TC-11-3: Test docker restart endpoint")
    # @pytest.mark.depends(on=["test_service_allocate_hotkey"])
    # def test_service_restart_docker(self):
    #     """Test /service/restart_docker"""
    #     query_params = {"hotkey": self.hotkey, "uuid_key": TestAPI.uuid}
    #     response = self.send_post_request("/service/restart_docker", query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource restarted successfully."

    # @allure.testcase("TC-11-4: Test docker exchange key endpoint")
    # @pytest.mark.depends(on=["test_service_allocate_hotkey"])
    # def test_service_exchange_key_docker(self):
    #     """Test /service/exchange_docker_key"""
    #     query_params = {"hotkey": self.hotkey, "uuid_key": TestAPI.uuid, "ssh_key": "test_key"}
    #     response = self.send_post_request("/service/exchange_docker_key", query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource ssh_key is exchanged successfully."

    # @allure.testcase("TC-11-5: Test de-allocation hotkey endpoint")
    # @pytest.mark.depends(on=["test_service_allocate_hotkey"])
    # def test_service_deallocation_hotkey(self):
    #     """Test /service/deallocate"""
    #     query_params = {"hotkey": self.hotkey, "uuid_key": TestAPI.uuid}
    #     response = self.send_post_request("/service/deallocate", query_params=query_params)

    #     assert response.status_code == 200
    #     resp_body = response.json()
    #     assert resp_body["success"] == True
    #     assert resp_body["message"] == "Resource deallocated successfully."
